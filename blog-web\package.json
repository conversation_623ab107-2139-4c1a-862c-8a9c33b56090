{"name": "blog-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@bytemd/plugin-gfm": "^1.22.0", "@bytemd/plugin-highlight": "^1.22.0", "@bytemd/plugin-math": "^1.22.0", "@bytemd/plugin-mermaid": "^1.22.0", "@bytemd/vue-next": "^1.22.0", "@toast-ui/editor": "^3.2.2", "axios": "^1.4.0", "element-plus": "^2.3.8", "js-cookie": "^3.0.5", "marked": "^16.1.1", "nprogress": "^0.2.0", "pinia": "^2.1.4", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "cypress": "^14.5.1", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "sass": "^1.64.1", "vite": "^4.4.6"}}